'use client';

import React, { forwardRef, useRef, useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/lib/stores';
import { generateAutoCompletion } from '@/lib/api';
import { toast } from 'sonner';

interface RichTextInputProps {
  value: string;
  onChange: (value: string) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  onCompositionStart?: () => void;
  onCompositionEnd?: () => void;
  onPaste?: (e: React.ClipboardEvent) => void;
  placeholder?: string;
  className?: string;
  messageInput?: boolean;
  shiftEnter?: boolean;
  largeTextAsFile?: boolean;
  autocomplete?: boolean;
  disabled?: boolean;
  selectedModels?: string[];
  generateAutoCompletion?: (text: string) => Promise<string>;
}

export const RichTextInput = forwardRef<HTMLDivElement, RichTextInputProps>(({
  value,
  onChange,
  onKeyDown,
  onCompositionStart,
  onCompositionEnd,
  onPaste,
  placeholder = '',
  className,
  messageInput = false,
  shiftEnter = true,
  largeTextAsFile = false,
  autocomplete = false,
  disabled = false,
  selectedModels = [],
  generateAutoCompletion
}, ref) => {
  const { token } = useAuthStore();
  const contentRef = useRef<HTMLDivElement>(null);
  const [isComposing, setIsComposing] = useState(false);
  const [showPlaceholder, setShowPlaceholder] = useState(!value);
  const [autocompleteSuggestion, setAutocompleteSuggestion] = useState('');
  const [isGeneratingCompletion, setIsGeneratingCompletion] = useState(false);

  // Update content when value changes
  useEffect(() => {
    if (contentRef.current && contentRef.current.textContent !== value) {
      contentRef.current.textContent = value;
      setShowPlaceholder(!value);
    }
  }, [value]);

  // Auto-resize for message input
  useEffect(() => {
    if (messageInput && contentRef.current) {
      const element = contentRef.current;
      element.style.height = 'auto';
      element.style.height = `${Math.min(element.scrollHeight, 200)}px`;
    }
  }, [value, messageInput]);

  const handleInput = (e: React.FormEvent<HTMLDivElement>) => {
    const target = e.currentTarget;
    const newValue = target.textContent || '';

    setShowPlaceholder(!newValue);
    onChange(newValue);

    // Check for large text
    if (largeTextAsFile && newValue.length > 4000) {
      // Handle large text as file
      const blob = new Blob([newValue], { type: 'text/plain' });
      const file = new File([blob], 'large-text.txt', { type: 'text/plain' });

      // This would trigger file upload logic
      // File upload logic would go here
    }

    // Handle autocomplete
    if (autocomplete && newValue.trim() && !isGeneratingCompletion) {
      handleAutoComplete(newValue);
    }
  };

  const handleAutoComplete = async (text: string) => {
    // Check if models are selected
    if (!selectedModels.length || selectedModels.includes('')) {
      toast.error('Model not selected');
      return;
    }

    if (!token) {
      return;
    }

    setIsGeneratingCompletion(true);

    try {
      let suggestion = '';

      if (generateAutoCompletion) {
        // Use custom completion function if provided
        suggestion = await generateAutoCompletion(text);
      } else {
        // Use default API
        const modelId = selectedModels[0];
        suggestion = await generateAutoCompletion(token, modelId, text);
      }

      if (suggestion && suggestion.trim()) {
        setAutocompleteSuggestion(suggestion);
      }
    } catch (error) {
      console.error('Failed to generate autocomplete:', error);
    } finally {
      setIsGeneratingCompletion(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (isComposing) return;

    // Handle Tab key for autocomplete
    if (e.key === 'Tab' && autocomplete && autocompleteSuggestion) {
      e.preventDefault();
      // Accept the autocomplete suggestion
      const newValue = value + autocompleteSuggestion;
      onChange(newValue);
      setAutocompleteSuggestion('');
      return;
    }

    // Handle Enter key behavior
    if (e.key === 'Enter') {
      if (shiftEnter && !e.shiftKey) {
        // Prevent default Enter behavior if shift+enter is required
        e.preventDefault();
        return;
      } else if (!shiftEnter && e.shiftKey) {
        // Allow new line with shift+enter
        return;
      }
    }

    // Handle Tab key for indentation (when not using autocomplete)
    if (e.key === 'Tab' && !autocomplete) {
      e.preventDefault();
      document.execCommand('insertText', false, '  '); // Insert 2 spaces
      return;
    }

    // Clear autocomplete suggestion on any other key
    if (autocompleteSuggestion) {
      setAutocompleteSuggestion('');
    }

    onKeyDown?.(e);
  };

  const handleCompositionStart = () => {
    setIsComposing(true);
    onCompositionStart?.();
  };

  const handleCompositionEnd = () => {
    setIsComposing(false);
    onCompositionEnd?.();
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLDivElement>) => {
    // Handle paste events
    const clipboardData = e.clipboardData;
    const pastedText = clipboardData.getData('text/plain');
    
    // Check for large pasted content
    if (largeTextAsFile && pastedText.length > 4000) {
      e.preventDefault();
      
      // Convert large text to file
      const blob = new Blob([pastedText], { type: 'text/plain' });
      const file = new File([blob], 'pasted-text.txt', { type: 'text/plain' });
      
      // File upload logic would go here
      return;
    }

    onPaste?.(e);
  };

  const handleFocus = () => {
    setShowPlaceholder(false);
  };

  const handleBlur = () => {
    const content = contentRef.current?.textContent || '';
    setShowPlaceholder(!content);
  };

  return (
    <div className="relative">
      {/* Placeholder */}
      {showPlaceholder && (
        <div className="absolute inset-0 pointer-events-none flex items-start pt-2 px-3 text-gray-500 dark:text-gray-400">
          {placeholder}
        </div>
      )}

      {/* Rich text input */}
      <div
        ref={contentRef}
        contentEditable={!disabled}
        suppressContentEditableWarning={true}
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        onCompositionStart={handleCompositionStart}
        onCompositionEnd={handleCompositionEnd}
        onPaste={handlePaste}
        onFocus={handleFocus}
        onBlur={handleBlur}
        className={cn(
          'w-full p-3 rounded-md',
          'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
          'resize-none overflow-y-auto',
          messageInput && 'min-h-[2.5rem] max-h-[200px]',
          disabled && 'opacity-50 cursor-not-allowed',
          className
        )}
        style={{
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word'
        }}
      />

      {/* Character count for large text warning */}
      {largeTextAsFile && value.length > 3000 && (
        <div className="absolute bottom-2 right-2 text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 px-2 py-1 rounded border">
          {value.length}/4000
          {value.length > 4000 && (
            <span className="text-orange-500 ml-1">
              (will be converted to file)
            </span>
          )}
        </div>
      )}

      {/* Autocomplete suggestions */}
      {autocomplete && autocompleteSuggestion && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg z-10 p-3">
          <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Press Tab to accept suggestion:
          </div>
          <div className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-2 rounded">
            {autocompleteSuggestion}
          </div>
        </div>
      )}

      {/* Loading indicator for autocomplete */}
      {autocomplete && isGeneratingCompletion && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg z-10 p-3">
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 dark:border-white mr-2"></div>
            Generating suggestion...
          </div>
        </div>
      )}
    </div>
  );
});
